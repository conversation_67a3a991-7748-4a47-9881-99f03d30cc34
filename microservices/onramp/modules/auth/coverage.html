
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>auth: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">synapse-its.com/onramp/modules/auth/handler.go (77.9%)</option>
				
				<option value="file1">synapse-its.com/onramp/modules/auth/service.go (100.0%)</option>
				
				<option value="file2">synapse-its.com/onramp/modules/auth/storage.go (100.0%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package auth

import (
        "context"
        "net"
        "net/http"
        "os"
        "strings"
        "time"

        "github.com/coreos/go-oidc"
        "github.com/gorilla/mux"
        "golang.org/x/oauth2"
        "synapse-its.com/shared/api/authorizer"
        "synapse-its.com/shared/api/response"
        "synapse-its.com/shared/connect"
        "synapse-its.com/shared/logger"
        "synapse-its.com/shared/rest/onramp/session"
        "synapse-its.com/shared/util"
)

// Handler handles authentication-related HTTP requests
type Handler struct {
        authService  AuthService
        sessionStore session.SessionStore
}

// NewHandler creates a new auth handler instance
func NewHandler(authService AuthService) *Handler <span class="cov8" title="1">{
        return &amp;Handler{
                authService:  authService,
                sessionStore: session.NewSessionStore(),
        }
}</span>

func (h *Handler) RegisterRoutes(router *mux.Router) <span class="cov8" title="1">{
        router.Methods(http.MethodPost).Path("/login").HandlerFunc(h.BasicAuth)
        router.Methods(http.MethodPost).Path("/register").HandlerFunc(h.Register)
        router.Methods(http.MethodGet).Path("/login").HandlerFunc(h.OAuth2Login)
        router.Methods(http.MethodGet).Path("/callback").HandlerFunc(h.OAuth2Callback)
        router.Methods(http.MethodGet).Path("/logout").HandlerFunc(h.OAuth2Logout)
}</span>

// BasicAuth handles POST /login requests
func (h *Handler) BasicAuth(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        isDev := strings.HasPrefix(r.Host, "localhost:4200")

        // Only allow POST method
        if r.Method != http.MethodPost </span><span class="cov8" title="1">{
                response.CreateMethodNotAllowedResponse(w)
                return
        }</span>

        // Parse request body
        <span class="cov8" title="1">var req BasicAuthRequest
        if err := r.ParseForm(); err != nil </span><span class="cov8" title="1">{
                response.CreateBadRequestResponse(w)
                return
        }</span>
        <span class="cov8" title="1">req.Username = r.FormValue("username")
        req.Password = r.FormValue("password")

        // Call the auth service
        resp, err := h.authService.BasicAuth(r.Context(), &amp;req)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorf("error - BasicAuth: %v", err.Error())
                response.CreateUnauthorizedResponse(w)
                return
        }</span>

        // Create session
        <span class="cov8" title="1">_, err = h.createSessionWithCookie(r.Context(), w, resp.User.ID.String(), nil, isDev)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorf("error - createSessionWithCookie: %v", err.Error())
                response.CreateInternalErrorResponse(w)
                return
        }</span>

        <span class="cov8" title="1">response.CreateSuccessResponse(resp, w)</span>
}

// Register handles POST /register requests
func (h *Handler) Register(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        // Only allow POST method
        if r.Method != http.MethodPost </span><span class="cov8" title="1">{
                response.CreateMethodNotAllowedResponse(w)
                return
        }</span>

        // Parse request body
        <span class="cov8" title="1">var req RegisterRequest
        if err := r.ParseForm(); err != nil </span><span class="cov8" title="1">{
                response.CreateBadRequestResponse(w)
                return
        }</span>
        <span class="cov8" title="1">req.FirstName = r.FormValue("firstname")
        req.LastName = r.FormValue("lastname")
        req.Username = r.FormValue("username")
        req.Password = r.FormValue("password")
        req.Email = r.FormValue("email")

        // Call the auth service
        err := h.authService.Register(r.Context(), &amp;req)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorf("error - Register: %v", err.Error())
                response.CreateInternalErrorResponse(w)
                return
        }</span>

        // Return success response
        <span class="cov8" title="1">response.CreateSuccessResponse(nil, w)</span>
}

// OAuth2Login handles GET /login requests
func (h *Handler) OAuth2Login(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        handleLogin(w, r)
}</span>

// OAuth2Callback handles GET /callback requests
func (h *Handler) OAuth2Callback(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        ctx := oidc.ClientContext(r.Context(), LocalhostHTTPProxy)
        isDev := strings.HasPrefix(r.Host, "localhost:4200")

        // Verify state cookie
        st, err := r.Cookie("oauth_state")
        if err != nil || r.URL.Query().Get("state") != st.Value </span><span class="cov8" title="1">{
                logger.Errorf("error - OAuth2Callback: invalid state")
                response.CreateBadRequestResponse(w)
                return
        }</span>

        // Delete the state cookie so it can't be reused
        <span class="cov8" title="1">http.SetCookie(w, &amp;http.Cookie{
                Name:     "oauth_state",
                Value:    "",
                Path:     "/",
                HttpOnly: true,
                Secure:   !isDev,
                Expires:  time.Unix(0, 0),
                MaxAge:   -1,
                SameSite: http.SameSiteLaxMode,
        })

        // Process OAuth2 callback
        loginResp, err := h.processOAuth2CallbackLogic(ctx, r, isDev)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorf("error - OAuth2Callback: %v", err.Error())
                response.CreateInternalErrorResponse(w)
                return
        }</span>

        <span class="cov0" title="0">_, err = h.createSessionWithCookie(ctx, w, loginResp.User.ID.String(), loginResp.OAuthToken, isDev)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorf("error - OAuth2Callback: %v", err.Error())
                response.CreateInternalErrorResponse(w)
                return
        }</span>

        <span class="cov0" title="0">logger.Infof("User authenticated: %s (%s)", loginResp.User.FirstName+" "+loginResp.User.LastName, loginResp.User.ID)
        http.Redirect(w, r, "/", http.StatusFound)</span>
}

// processOAuth2CallbackLogic handles the OAuth2 token exchange and user authentication
func (h *Handler) processOAuth2CallbackLogic(ctx context.Context, r *http.Request, isDev bool) (*OAuth2CallbackResponse, error) <span class="cov8" title="1">{
        // Choose the OIDC configuration based on the request host
        oidcConfig := map[bool]*OIDCConfig{
                false: &amp;SynapseOIDC,
                true:  &amp;SynapseOIDCLocal,
        }[isDev]

        // Exchange code for token
        token, err := oidcConfig.OAuth2Config.Exchange(ctx, r.URL.Query().Get("code"))
        if err != nil </span><span class="cov8" title="1">{
                return nil, ErrExchangeFailed
        }</span>

        // Verify the ID Token and extract claims
        <span class="cov0" title="0">rawID, ok := token.Extra("id_token").(string)
        if !ok </span><span class="cov0" title="0">{
                return nil, ErrMissingIDToken
        }</span>

        <span class="cov0" title="0">idToken, err := oidcConfig.Verifier.Verify(ctx, rawID)
        if err != nil </span><span class="cov0" title="0">{
                return nil, ErrInvalidIDToken
        }</span>

        // Extract claims from ID token
        <span class="cov0" title="0">var claims map[string]interface{}
        if err := idToken.Claims(&amp;claims); err != nil </span><span class="cov0" title="0">{
                return nil, ErrInvalidIDToken
        }</span>

        // Get state cookie for validation
        <span class="cov0" title="0">st, err := r.Cookie("oauth_state")
        stateCookie := ""
        if err == nil </span><span class="cov0" title="0">{
                stateCookie = st.Value
        }</span>

        // Create callback request
        <span class="cov0" title="0">callbackReq := &amp;OAuth2CallbackRequest{
                Code:        r.URL.Query().Get("code"),
                State:       r.URL.Query().Get("state"),
                StateCookie: stateCookie,
                IsDev:       isDev,
                Claims:      claims,
                OAuthToken:  token,
        }

        // Process through service layer
        return h.authService.ProcessOAuth2Callback(r.Context(), callbackReq)</span>
}

// OAuth2Logout handles GET /logout requests
func (h *Handler) OAuth2Logout(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        h.handleLogout(w, r)
}</span>

var (
        // synapseOIDC is a global variable that holds the OIDC configuration
        // for the Synapse OAuth2 server.
        SynapseOIDC = OIDCConfig{
                ClientID:     os.Getenv("SYNAPSE_OIDC_CLIENT_ID"),
                ClientSecret: os.Getenv("SYNAPSE_OIDC_CLIENT_SECRET"),
                RedirectURL:  os.Getenv("SYNAPSE_OIDC_CLIENT_CALLBACK_URL"),
                IssuerURL:    os.Getenv("SYNAPSE_OIDC_ISSUER_URL"),
        }

        // synapseOIDCLocal is a local version of the OIDC configuration
        // used for development purposes, pointing to keycloak.
        SynapseOIDCLocal OIDCConfig

        SynapseOIDCScopes = []string{oidc.ScopeOpenID, "profile", "email"}

        // localhostHTTPProxy is a custom HTTP client that rewrites requests
        // to "localhost:8091" to "host.docker.internal:8091". This is necessary
        // for the OIDC provider to communicate with the host machine from within
        // a Docker container, as Docker containers cannot directly access services
        // running on the host machine using "localhost".  This is not a security
        // concern in prod, because in production, the request will simply fail
        // because there is no OIDC provider listening there.
        LocalhostHTTPProxy = &amp;http.Client{Transport: &amp;http.Transport{
                DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) <span class="cov8" title="1">{
                        if strings.HasSuffix(addr, "localhost:8091") </span><span class="cov8" title="1">{
                                addr = "host.docker.internal:8091"
                        }</span>
                        <span class="cov8" title="1">return (&amp;net.Dialer{}).DialContext(ctx, network, addr)</span>
                },
        }}
)

// GetSessionStore returns the session store (for testing)
func (h *Handler) GetSessionStore() session.SessionStore <span class="cov8" title="1">{
        return h.sessionStore
}</span>

// OIDCConfig holds the configuration for OpenID Connect (OIDC) authentication.
type OIDCConfig struct {
        ClientID     string
        ClientSecret string
        RedirectURL  string
        IssuerURL    string
        Provider     *oidc.Provider
        OAuth2Config *oauth2.Config
        Verifier     *oidc.IDTokenVerifier
        Scope        string
}

func handleLogin(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        isDev := strings.HasPrefix(r.Host, "localhost:4200")
        state := util.RandomString(32)

        // Set the state cookie
        http.SetCookie(w, &amp;http.Cookie{
                Name:     "oauth_state",
                Value:    state,
                Path:     "/",
                HttpOnly: true,
                Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
                SameSite: http.SameSiteLaxMode,
        })

        // Choose the OIDC configuration based on the request host
        cfg := map[bool]*oauth2.Config{
                false: SynapseOIDC.OAuth2Config,
                true:  SynapseOIDCLocal.OAuth2Config,
        }[isDev]

        // Redirect to the OIDC provider's authorization endpoint
        http.Redirect(w, r, cfg.AuthCodeURL(state), http.StatusFound)
}</span>

func (h *Handler) handleLogout(w http.ResponseWriter, r *http.Request) <span class="cov8" title="1">{
        isDev := strings.HasPrefix(r.Host, "localhost:4200")

        // Grab the session_id cookie (if any)
        cookie, err := r.Cookie("session_id")
        if err == nil </span><span class="cov8" title="1">{
                // Delete the server‐side session
                h.sessionStore.ClearSession(cookie.Value)
        }</span>

        // Clear the cookie in the browser
        <span class="cov8" title="1">http.SetCookie(w, &amp;http.Cookie{
                Name:     "session_id",
                Value:    "",
                Path:     "/",
                HttpOnly: true,
                Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
                Expires:  time.Unix(0, 0),
                MaxAge:   -1,
                SameSite: http.SameSiteNoneMode,
        })

        // Redirect to its end-session endpoint:
        // NOTE: This would log the user out of the OIDC provider, but it is not
        // strictly necessary for our use case, since we are not using the OIDC
        // provider for anything other than authentication.  This code is left here
        // for reference, in case we want to implement a full logout flow in the
        // future.
        //
        // redirectURI := url.QueryEscape(oidcConfig.RedirectURL)
        // logoutURL := oidcConfig.IssuerURL +
        //         "/protocol/openid-connect/logout?redirect_uri=" + redirectURI
        // http.Redirect(w, r, logoutURL, http.StatusFound)

        http.Redirect(w, r, "/", http.StatusFound)</span>
}

// createSessionWithCookie creates a session with permissions and sets the session cookie
func (h *Handler) createSessionWithCookie(ctx context.Context, w http.ResponseWriter, userID string, oauthToken *oauth2.Token, isDev bool) (string, error) <span class="cov8" title="1">{
        // Get database connections for permissions
        connections, err := connect.GetConnections(ctx)
        var userPermissions *authorizer.UserPermissions
        if err == nil </span><span class="cov8" title="1">{
                // Get user permissions
                userPermissions, err = authorizer.GetUserPermissions(connections.Postgres, userID)
        }</span>
        <span class="cov8" title="1">if err != nil </span><span class="cov8" title="1">{
                logger.Warnf("Failed to load user permissions for %s: %v", userID, err)
                // Continue without permissions rather than failing the login
                userPermissions = &amp;authorizer.UserPermissions{
                        UserID:      userID,
                        Permissions: []authorizer.Permission{},
                }
        }</span>
        // Create a session with comprehensive session data
        <span class="cov8" title="1">sessionID := util.RandomString(32)
        sessionData := &amp;session.SessionData{
                UserID:          userID,
                OAuthToken:      oauthToken,
                UserPermissions: userPermissions,
                Data:            make(map[string]any),
        }
        h.sessionStore.SetSession(sessionID, sessionData)

        http.SetCookie(w, &amp;http.Cookie{
                Name:     "session_id",
                Value:    sessionID,
                Path:     "/",
                HttpOnly: true,
                Secure:   !isDev,
                SameSite: http.SameSiteStrictMode,
        })

        return sessionID, nil</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package auth

import (
        "context"
        "strings"

        "synapse-its.com/onramp/domain"
        "synapse-its.com/onramp/pkg"
)

// AuthService defines the interface for authentication business logic
type AuthService interface {
        BasicAuth(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error)
        Register(ctx context.Context, req *RegisterRequest) error
        HandleOIDCLogin(ctx context.Context, req *OIDCLoginRequest) (*LoginResponse, error)
        ProcessOAuth2Callback(ctx context.Context, req *OAuth2CallbackRequest) (*OAuth2CallbackResponse, error)
}

// service implements the AuthService interface
type service struct {
        authRepo       domain.AuthRepository
        passwordHasher pkg.PasswordHasher
        tokenGenerator pkg.TokenGenerator
}

// NewService creates a new auth service instance
func NewService(authRepo domain.AuthRepository, passwordHasher pkg.PasswordHasher, tokenGenerator pkg.TokenGenerator) AuthService <span class="cov8" title="1">{
        return &amp;service{
                authRepo:       authRepo,
                passwordHasher: passwordHasher,
                tokenGenerator: tokenGenerator,
        }
}</span>

// BasicAuth authenticates a user with username and password
func (s *service) BasicAuth(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) <span class="cov8" title="1">{
        // Validate input
        if req.Username == "" || req.Password == "" </span><span class="cov8" title="1">{
                return nil, domain.ErrInvalidCredentials
        }</span>

        // Get user and auth method by username
        <span class="cov8" title="1">user, authMethod, err := s.authRepo.GetByUsername(ctx, req.Username)
        if err != nil </span><span class="cov8" title="1">{
                return nil, err
        }</span>

        <span class="cov8" title="1">if user == nil &amp;&amp; authMethod == nil </span><span class="cov8" title="1">{
                return nil, domain.ErrUserNotFound
        }</span>

        <span class="cov8" title="1">if user == nil || authMethod == nil </span><span class="cov8" title="1">{
                return nil, domain.ErrInvalidCredentials
        }</span>

        // Check if auth method is enabled
        <span class="cov8" title="1">if !authMethod.IsEnabled </span><span class="cov8" title="1">{
                return nil, domain.ErrAccountDisabled
        }</span>

        // Verify password
        <span class="cov8" title="1">if !s.passwordHasher.ComparePassword(req.Password, authMethod.PasswordHash) </span><span class="cov8" title="1">{
                return nil, domain.ErrInvalidCredentials
        }</span>

        // Update last login
        <span class="cov8" title="1">if err = s.authRepo.UpdateLastLogin(ctx, user, authMethod); err != nil </span><span class="cov8" title="1">{
                return nil, err
        }</span>

        // Generate a token
        <span class="cov8" title="1">token, _, err := s.tokenGenerator.GenerateToken(req.Username)
        if err != nil </span><span class="cov8" title="1">{
                return nil, err
        }</span>

        // TODO: Store the LoginResponse in the session store and set the cookie for FE
        <span class="cov8" title="1">return &amp;LoginResponse{
                User:  user,
                Token: token,
        }, nil</span>
}

func (s *service) Register(ctx context.Context, req *RegisterRequest) error <span class="cov8" title="1">{
        // Validate input
        if req.Username == "" || req.Password == "" </span><span class="cov8" title="1">{
                return domain.ErrInvalidInput
        }</span>

        // Check if user already exists
        <span class="cov8" title="1">checkUserExists := func() bool </span><span class="cov8" title="1">{
                existingUser, existingAuthMethod, _ := s.authRepo.GetByUsername(ctx, req.Username)
                if existingUser == nil || existingAuthMethod == nil </span><span class="cov8" title="1">{
                        return false
                }</span>
                <span class="cov8" title="1">return strings.EqualFold(existingAuthMethod.UserName, req.Username)</span>
        }

        <span class="cov8" title="1">if checkUserExists() </span><span class="cov8" title="1">{
                return domain.ErrUserAlreadyExists
        }</span>

        // Hash the password
        <span class="cov8" title="1">passwordHash := s.passwordHasher.HashPassword(req.Password)

        // Create domain objects
        user := &amp;domain.User{
                // User ID will be set by the database in CreateBasicAuthUser
                FirstName: req.FirstName,
                LastName:  req.LastName,
        }

        authMethod := &amp;domain.AuthMethod{
                // AuthMethodID will be auto-generated by database
                // UserID will be set after user creation
                Type:         domain.AuthMethodTypeUsernamePassword,
                UserName:     req.Username,
                PasswordHash: passwordHash,
                Email:        req.Email,
        }

        // Create user in database (this will populate user.ID with the database-generated UUID)
        if err := s.authRepo.CreateBasicAuthUser(ctx, user, authMethod); err != nil </span><span class="cov8" title="1">{
                return err
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// HandleOIDCLogin processes OIDC authentication, creating users if they don't exist
func (s *service) HandleOIDCLogin(ctx context.Context, req *OIDCLoginRequest) (*LoginResponse, error) <span class="cov8" title="1">{
        // Validate input
        if req.Subject == "" || req.Issuer == "" </span><span class="cov8" title="1">{
                return nil, domain.ErrInvalidCredentials
        }</span>

        // Check if user already exists by OIDC subject
        <span class="cov8" title="1">user, authMethod, err := s.authRepo.GetByOIDCSubject(ctx, req.Issuer, req.Subject)
        if err != nil </span><span class="cov8" title="1">{
                return nil, err
        }</span>

        // If user doesn't exist, create them
        <span class="cov8" title="1">if user == nil || authMethod == nil </span><span class="cov8" title="1">{
                // Parse name into first/last name
                firstName := ""
                lastName := ""
                if req.Name != "" </span><span class="cov8" title="1">{
                        nameParts := strings.Fields(req.Name)
                        if len(nameParts) &gt; 0 </span><span class="cov8" title="1">{
                                firstName = nameParts[0]
                        }</span>
                        <span class="cov8" title="1">if len(nameParts) &gt; 1 </span><span class="cov8" title="1">{
                                lastName = strings.Join(nameParts[1:], " ")
                        }</span>
                }

                // Create new user
                <span class="cov8" title="1">user = &amp;domain.User{
                        // User ID will be set by the database in CreateOIDCUser
                        FirstName: firstName,
                        LastName:  lastName,
                }

                // Create new OIDC auth method
                authMethod = &amp;domain.AuthMethod{
                        // AuthMethodID will be auto-generated by database
                        // UserID will be set after user creation
                        Type:   domain.AuthMethodTypeOIDC,
                        Sub:    req.Subject,
                        Issuer: req.Issuer,
                        Email:  req.Email,
                }

                // Create user and auth method in database
                if err := s.authRepo.CreateOIDCUser(ctx, user, authMethod); err != nil </span><span class="cov8" title="1">{
                        return nil, err
                }</span>
        } else<span class="cov8" title="1"> {
                // Update last login for existing user
                if err = s.authRepo.UpdateLastLogin(ctx, user, authMethod); err != nil </span><span class="cov8" title="1">{
                        return nil, err
                }</span>
        }

        // Generate a token (reusing existing token generation)
        <span class="cov8" title="1">token, _, err := s.tokenGenerator.GenerateToken(user.ID.String())
        if err != nil </span><span class="cov8" title="1">{
                return nil, err
        }</span>

        <span class="cov8" title="1">return &amp;LoginResponse{
                User:  user,
                Token: token,
        }, nil</span>
}

// ProcessOAuth2Callback handles the OAuth2 callback business logic
func (s *service) ProcessOAuth2Callback(ctx context.Context, req *OAuth2CallbackRequest) (*OAuth2CallbackResponse, error) <span class="cov8" title="1">{
        // Validate state
        if req.State != req.StateCookie </span><span class="cov8" title="1">{
                return nil, domain.ErrInvalidCredentials
        }</span>

        // Extract required claims
        <span class="cov8" title="1">sub, ok := req.Claims["sub"].(string)
        if !ok </span><span class="cov8" title="1">{
                return nil, domain.ErrInvalidCredentials
        }</span>
        <span class="cov8" title="1">iss, ok := req.Claims["iss"].(string)
        if !ok </span><span class="cov8" title="1">{
                return nil, domain.ErrInvalidCredentials
        }</span>

        <span class="cov8" title="1">email, _ := req.Claims["email"].(string)
        name, _ := req.Claims["name"].(string)

        // Create OIDC login request
        oidcLoginReq := &amp;OIDCLoginRequest{
                Subject: sub,
                Issuer:  iss,
                Email:   email,
                Name:    name,
        }

        // Process OIDC login
        loginResp, err := s.HandleOIDCLogin(ctx, oidcLoginReq)
        if err != nil </span><span class="cov8" title="1">{
                return nil, err
        }</span>

        // Return the login response
        <span class="cov8" title="1">return &amp;OAuth2CallbackResponse{
                User:  loginResp.User,
                Token: loginResp.Token,
        }, nil</span>
}
</pre>
		
		<pre class="file" id="file2" style="display: none">package auth

import (
        "context"
        "database/sql"
        "encoding/json"
        "fmt"
        "time"

        "github.com/google/uuid"
        "synapse-its.com/onramp/domain"
        "synapse-its.com/shared/connect"
)

type storage struct {
        db connect.DatabaseExecutor
}

func NewPostgresAuthRepository(db connect.DatabaseExecutor) domain.AuthRepository <span class="cov8" title="1">{
        return &amp;storage{
                db: db,
        }
}</span>

func (s *storage) GetByUsername(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) <span class="cov8" title="1">{
        query := `
                SELECT 
                        u.Id as user_id,
                        u.FirstName as user_firstname,
                        u.LastName as user_lastname,
                        u.Mobile as user_mobile,
                        u.IanaTimezone as user_ianatimezone,
                        u.Description as user_description,
                        am.Id as authmethod_id,
                        am.UserId as authmethod_userid,
                        am.Type as authmethod_type,
                        am.Sub as authmethod_sub,
                        am.Issuer as authmethod_issuer,
                        am.UserName as authmethod_username,
                        am.PasswordHash as authmethod_passwordhash,
                        am.Email as authmethod_email,
                        am.Metadata as authmethod_metadata,
                        am.IsEnabled as authmethod_isenabled
                FROM {{User}} u
                INNER JOIN {{AuthMethod}} am ON u.Id = am.UserId
                WHERE am.UserName = $1 
                AND am.Type = 'USERNAME_PASSWORD'
                AND am.IsDeleted = false
                AND u.IsDeleted = false
                LIMIT 1`

        var dbResult UserAuthMethod
        err := s.db.QueryRowStruct(&amp;dbResult, query, username)
        if err != nil </span><span class="cov8" title="1">{
                if err == sql.ErrNoRows </span><span class="cov8" title="1">{
                        return nil, nil, nil
                }</span>
                <span class="cov8" title="1">return nil, nil, err</span>
        }

        // Convert to domain objects
        <span class="cov8" title="1">user := &amp;domain.User{
                ID:           dbResult.UserID,
                FirstName:    stringValue(dbResult.FirstName),
                LastName:     stringValue(dbResult.LastName),
                Mobile:       stringValue(dbResult.Mobile),
                IanaTimezone: dbResult.IanaTimezone,
                Description:  stringValue(dbResult.Description),
        }

        authMethod := &amp;domain.AuthMethod{
                ID:           dbResult.AuthMethodID,
                UserID:       dbResult.AuthMethodUserID,
                Type:         domain.AuthMethodType(dbResult.Type),
                Sub:          stringValue(dbResult.Sub),
                Issuer:       stringValue(dbResult.Issuer),
                UserName:     stringValue(dbResult.UserName),
                PasswordHash: stringValue(dbResult.PasswordHash),
                Email:        stringValue(dbResult.Email),
                IsEnabled:    dbResult.IsEnabled,
        }

        // Parse metadata JSON
        if dbResult.Metadata != nil </span><span class="cov8" title="1">{
                if err := json.Unmarshal(dbResult.Metadata, &amp;authMethod.Metadata); err != nil </span><span class="cov8" title="1">{
                        // If metadata parsing fails, set to empty map
                        authMethod.Metadata = make(map[string]interface{})
                }</span>
        } else<span class="cov8" title="1"> {
                authMethod.Metadata = make(map[string]interface{})
        }</span>

        <span class="cov8" title="1">return user, authMethod, nil</span>
}

func (s *storage) UpdateLastLogin(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error <span class="cov8" title="1">{
        now := time.Now()

        // Update both User.LastLogin and AuthMethod.LastLogin
        query := `
                UPDATE {{User}} 
                SET LastLogin = $1, UpdatedAt = $1 
                WHERE Id = $2`

        _, err := s.db.Exec(query, now, user.ID)
        if err != nil </span><span class="cov8" title="1">{
                return err
        }</span>

        <span class="cov8" title="1">query = `
                UPDATE {{AuthMethod}} 
                SET LastLogin = $1, UpdatedAt = $1 
                WHERE Id = $2`

        _, err = s.db.Exec(query, now, authMethod.ID)
        return err</span>
}

func (s *storage) CreateBasicAuthUser(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error <span class="cov8" title="1">{
        // Insert user record and get the auto-generated ID
        userQuery := `
                INSERT INTO {{User}} (
                        FirstName, 
                        LastName 
                ) VALUES (
                        $1, $2
                )
                RETURNING Id`

        row, err := s.db.QueryRow(userQuery,
                user.FirstName,
                user.LastName,
        )
        if err != nil </span><span class="cov8" title="1">{
                return err
        }</span>

        // Extract the userID from the returned row
        // PostgreSQL returns column names in lowercase unless quoted
        <span class="cov8" title="1">userIDInterface, ok := row["id"]
        if !ok </span><span class="cov8" title="1">{
                return fmt.Errorf("ID column not returned from user insert")
        }</span>

        <span class="cov8" title="1">userID, ok := userIDInterface.(string)
        if !ok </span><span class="cov8" title="1">{
                return fmt.Errorf("ID column is not a string: %T", userIDInterface)
        }</span>

        // Update the user object with the generated ID
        <span class="cov8" title="1">parsedUUID, parseErr := uuid.Parse(userID)
        if parseErr != nil </span><span class="cov8" title="1">{
                return fmt.Errorf("failed to parse user ID as UUID: %w", parseErr)
        }</span>
        <span class="cov8" title="1">user.ID = parsedUUID

        // Update the auth method object with the user ID
        authMethod.UserID = parsedUUID

        // Insert auth method record (AuthMethod ID will be auto-generated)
        authMethodQuery := `
                INSERT INTO {{AuthMethod}} (
                        UserId, 
                        Type, 
                        UserName, 
                        PasswordHash, 
                        Email 
                ) VALUES (
                        $1, $2, $3, $4, $5
                )`

        _, err = s.db.Exec(authMethodQuery,
                userID,
                authMethod.Type,
                authMethod.UserName,
                authMethod.PasswordHash,
                authMethod.Email,
        )
        if err != nil </span><span class="cov8" title="1">{
                return err
        }</span>

        <span class="cov8" title="1">return nil</span>
}

func (s *storage) GetByOIDCSubject(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error) <span class="cov8" title="1">{
        query := `
                SELECT 
                        u.Id as user_id,
                        u.FirstName as user_firstname,
                        u.LastName as user_lastname,
                        u.Mobile as user_mobile,
                        u.IanaTimezone as user_ianatimezone,
                        u.Description as user_description,
                        am.Id as authmethod_id,
                        am.UserId as authmethod_userid,
                        am.Type as authmethod_type,
                        am.Sub as authmethod_sub,
                        am.Issuer as authmethod_issuer,
                        am.UserName as authmethod_username,
                        am.PasswordHash as authmethod_passwordhash,
                        am.Email as authmethod_email,
                        am.Metadata as authmethod_metadata,
                        am.IsEnabled as authmethod_isenabled
                FROM {{User}} u
                INNER JOIN {{AuthMethod}} am ON u.Id = am.UserId
                WHERE am.Issuer = $1 
                AND am.Sub = $2
                AND am.Type = 'OIDC'
                AND am.IsDeleted = false
                AND u.IsDeleted = false
                LIMIT 1`

        var dbResult UserAuthMethod
        err := s.db.QueryRowStruct(&amp;dbResult, query, issuer, subject)
        if err != nil </span><span class="cov8" title="1">{
                if err == sql.ErrNoRows </span><span class="cov8" title="1">{
                        return nil, nil, nil
                }</span>
                <span class="cov8" title="1">return nil, nil, err</span>
        }

        // Convert to domain objects
        <span class="cov8" title="1">user := &amp;domain.User{
                ID:           dbResult.UserID,
                FirstName:    stringValue(dbResult.FirstName),
                LastName:     stringValue(dbResult.LastName),
                Mobile:       stringValue(dbResult.Mobile),
                IanaTimezone: dbResult.IanaTimezone,
                Description:  stringValue(dbResult.Description),
        }

        authMethod := &amp;domain.AuthMethod{
                ID:           dbResult.AuthMethodID,
                UserID:       dbResult.AuthMethodUserID,
                Type:         domain.AuthMethodType(dbResult.Type),
                Sub:          stringValue(dbResult.Sub),
                Issuer:       stringValue(dbResult.Issuer),
                UserName:     stringValue(dbResult.UserName),
                PasswordHash: stringValue(dbResult.PasswordHash),
                Email:        stringValue(dbResult.Email),
                IsEnabled:    dbResult.IsEnabled,
        }

        // Parse metadata JSON
        if dbResult.Metadata != nil </span><span class="cov8" title="1">{
                if err := json.Unmarshal(dbResult.Metadata, &amp;authMethod.Metadata); err != nil </span><span class="cov8" title="1">{
                        // If metadata parsing fails, set to empty map
                        authMethod.Metadata = make(map[string]interface{})
                }</span>
        } else<span class="cov8" title="1"> {
                authMethod.Metadata = make(map[string]interface{})
        }</span>

        <span class="cov8" title="1">return user, authMethod, nil</span>
}

func (s *storage) CreateOIDCUser(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error <span class="cov8" title="1">{
        // Insert user record and get the auto-generated ID
        userQuery := `
                INSERT INTO {{User}} (
                        FirstName, 
                        LastName,
                        LastLogin
                ) VALUES (
                        $1, $2, NOW()
                )
                RETURNING Id`

        row, err := s.db.QueryRow(userQuery,
                user.FirstName,
                user.LastName,
        )
        if err != nil </span><span class="cov8" title="1">{
                return err
        }</span>

        // Extract the userID from the returned row
        // PostgreSQL returns column names in lowercase unless quoted
        <span class="cov8" title="1">userIDInterface, ok := row["id"]
        if !ok </span><span class="cov8" title="1">{
                return fmt.Errorf("ID column not returned from user insert")
        }</span>

        <span class="cov8" title="1">userID, ok := userIDInterface.(string)
        if !ok </span><span class="cov8" title="1">{
                return fmt.Errorf("ID column is not a string: %T", userIDInterface)
        }</span>

        // Update the user object with the generated ID
        <span class="cov8" title="1">parsedUUID, parseErr := uuid.Parse(userID)
        if parseErr != nil </span><span class="cov8" title="1">{
                return fmt.Errorf("failed to parse user ID as UUID: %w", parseErr)
        }</span>
        <span class="cov8" title="1">user.ID = parsedUUID

        // Update the auth method object with the user ID
        authMethod.UserID = parsedUUID

        // Insert auth method record (AuthMethod ID will be auto-generated)
        authMethodQuery := `
                INSERT INTO {{AuthMethod}} (
                        UserId, 
                        Type, 
                        Sub,
                        Issuer,
                        Email 
                ) VALUES (
                        $1, $2, $3, $4, $5
                )`

        _, err = s.db.Exec(authMethodQuery,
                userID,
                authMethod.Type,
                authMethod.Sub,
                authMethod.Issuer,
                authMethod.Email,
        )
        if err != nil </span><span class="cov8" title="1">{
                return err
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// stringValue safely converts a pointer to string to a string value
func stringValue(s *string) string <span class="cov8" title="1">{
        if s == nil </span><span class="cov8" title="1">{
                return ""
        }</span>
        <span class="cov8" title="1">return *s</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
