package auth

import (
	"context"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/coreos/go-oidc"
	"github.com/gorilla/mux"
	"golang.org/x/oauth2"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/rest/onramp/session"
	"synapse-its.com/shared/util"
)

// OAuth2Exchanger interface for OAuth2 token exchange operations
type OAuth2Exchanger interface {
	Exchange(ctx context.Context, code string) (*oauth2.Token, error)
}

// IDTokenVerifier interface for ID token verification operations
type IDTokenVerifier interface {
	Verify(ctx context.Context, rawIDToken string) (IDTokenClaims, error)
}

// IDTokenClaims interface for extracting claims from ID tokens
type IDTokenClaims interface {
	Claims(v interface{}) error
}

// TestableOIDCConfig holds testable OAuth2 and OIDC components
type TestableOIDCConfig struct {
	OAuth2Exchanger OAuth2Exchanger
	IDVerifier      IDTokenVerifier
}

// Hand<PERSON> handles authentication-related HTTP requests
type Handler struct {
	authService  AuthService
	sessionStore session.SessionStore
	// For testing - allows injection of OAuth2 components
	oauth2ConfigProvider func(isDev bool) *OIDCConfig
	// For testing - allows injection of testable components
	testableConfigProvider func(isDev bool) *TestableOIDCConfig
}

// NewHandler creates a new auth handler instance
func NewHandler(authService AuthService) *Handler {
	return &Handler{
		authService:            authService,
		sessionStore:           session.NewSessionStore(),
		oauth2ConfigProvider:   defaultOAuth2ConfigProvider,
		testableConfigProvider: nil, // Will use real OAuth2 by default
	}
}

// defaultOAuth2ConfigProvider is the default OAuth2 config provider
func defaultOAuth2ConfigProvider(isDev bool) *OIDCConfig {
	return map[bool]*OIDCConfig{
		false: &SynapseOIDC,
		true:  &SynapseOIDCLocal,
	}[isDev]
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	router.Methods(http.MethodPost).Path("/login").HandlerFunc(h.BasicAuth)
	router.Methods(http.MethodPost).Path("/register").HandlerFunc(h.Register)
	router.Methods(http.MethodGet).Path("/login").HandlerFunc(h.OAuth2Login)
	router.Methods(http.MethodGet).Path("/callback").HandlerFunc(h.OAuth2Callback)
	router.Methods(http.MethodGet).Path("/logout").HandlerFunc(h.OAuth2Logout)
}

// BasicAuth handles POST /login requests
func (h *Handler) BasicAuth(w http.ResponseWriter, r *http.Request) {
	isDev := strings.HasPrefix(r.Host, "localhost:4200")

	// Only allow POST method
	if r.Method != http.MethodPost {
		response.CreateMethodNotAllowedResponse(w)
		return
	}

	// Parse request body
	var req BasicAuthRequest
	if err := r.ParseForm(); err != nil {
		response.CreateBadRequestResponse(w)
		return
	}
	req.Username = r.FormValue("username")
	req.Password = r.FormValue("password")

	// Call the auth service
	resp, err := h.authService.BasicAuth(r.Context(), &req)
	if err != nil {
		logger.Errorf("error - BasicAuth: %v", err.Error())
		response.CreateUnauthorizedResponse(w)
		return
	}

	// Create session
	_, err = h.createSessionWithCookie(r.Context(), w, resp.User.ID.String(), nil, isDev)
	if err != nil {
		logger.Errorf("error - createSessionWithCookie: %v", err.Error())
		response.CreateInternalErrorResponse(w)
		return
	}

	response.CreateSuccessResponse(resp, w)
}

// Register handles POST /register requests
func (h *Handler) Register(w http.ResponseWriter, r *http.Request) {
	// Only allow POST method
	if r.Method != http.MethodPost {
		response.CreateMethodNotAllowedResponse(w)
		return
	}

	// Parse request body
	var req RegisterRequest
	if err := r.ParseForm(); err != nil {
		response.CreateBadRequestResponse(w)
		return
	}
	req.FirstName = r.FormValue("firstname")
	req.LastName = r.FormValue("lastname")
	req.Username = r.FormValue("username")
	req.Password = r.FormValue("password")
	req.Email = r.FormValue("email")

	// Call the auth service
	err := h.authService.Register(r.Context(), &req)
	if err != nil {
		logger.Errorf("error - Register: %v", err.Error())
		response.CreateInternalErrorResponse(w)
		return
	}

	// Return success response
	response.CreateSuccessResponse(nil, w)
}

// OAuth2Login handles GET /login requests
func (h *Handler) OAuth2Login(w http.ResponseWriter, r *http.Request) {
	handleLogin(w, r)
}

// OAuth2Callback handles GET /callback requests
func (h *Handler) OAuth2Callback(w http.ResponseWriter, r *http.Request) {
	ctx := oidc.ClientContext(r.Context(), LocalhostHTTPProxy)
	isDev := strings.HasPrefix(r.Host, "localhost:4200")

	// Verify state cookie
	st, err := r.Cookie("oauth_state")
	if err != nil || r.URL.Query().Get("state") != st.Value {
		logger.Errorf("error - OAuth2Callback: invalid state")
		response.CreateBadRequestResponse(w)
		return
	}

	// Delete the state cookie so it can't be reused
	http.SetCookie(w, &http.Cookie{
		Name:     "oauth_state",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		Secure:   !isDev,
		Expires:  time.Unix(0, 0),
		MaxAge:   -1,
		SameSite: http.SameSiteLaxMode,
	})

	// Process OAuth2 callback
	loginResp, err := h.processOAuth2CallbackLogic(ctx, r, isDev)
	if err != nil {
		logger.Errorf("error - OAuth2Callback: %v", err.Error())
		response.CreateInternalErrorResponse(w)
		return
	}

	_, err = h.createSessionWithCookie(ctx, w, loginResp.User.ID.String(), loginResp.OAuthToken, isDev)
	if err != nil {
		logger.Errorf("error - OAuth2Callback: %v", err.Error())
		response.CreateInternalErrorResponse(w)
		return
	}

	logger.Infof("User authenticated: %s (%s)", loginResp.User.FirstName+" "+loginResp.User.LastName, loginResp.User.ID)
	http.Redirect(w, r, "/", http.StatusFound)
}

// processOAuth2CallbackLogic handles the OAuth2 token exchange and user authentication
func (h *Handler) processOAuth2CallbackLogic(ctx context.Context, r *http.Request, isDev bool) (*OAuth2CallbackResponse, error) {
	// Check if we have a testable config for testing
	if h.testableConfigProvider != nil {
		return h.processOAuth2CallbackWithTestableConfig(ctx, r, isDev)
	}

	// Use real OAuth2 configuration
	oidcConfig := h.oauth2ConfigProvider(isDev)

	// Exchange code for token
	token, err := oidcConfig.OAuth2Config.Exchange(ctx, r.URL.Query().Get("code"))
	if err != nil {
		return nil, ErrExchangeFailed
	}

	// Verify the ID Token and extract claims
	rawID, ok := token.Extra("id_token").(string)
	if !ok {
		return nil, ErrMissingIDToken
	}

	idToken, err := oidcConfig.Verifier.Verify(ctx, rawID)
	if err != nil {
		return nil, ErrInvalidIDToken
	}

	// Extract claims from ID token
	var claims map[string]interface{}
	if err := idToken.Claims(&claims); err != nil {
		return nil, ErrInvalidIDToken
	}

	// Get state cookie for validation
	st, err := r.Cookie("oauth_state")
	stateCookie := ""
	if err == nil {
		stateCookie = st.Value
	}

	// Create callback request
	callbackReq := &OAuth2CallbackRequest{
		Code:        r.URL.Query().Get("code"),
		State:       r.URL.Query().Get("state"),
		StateCookie: stateCookie,
		IsDev:       isDev,
		Claims:      claims,
		OAuthToken:  token,
	}

	// Process through service layer
	return h.authService.ProcessOAuth2Callback(r.Context(), callbackReq)
}

// processOAuth2CallbackWithTestableConfig handles OAuth2 callback using testable components
func (h *Handler) processOAuth2CallbackWithTestableConfig(ctx context.Context, r *http.Request, isDev bool) (*OAuth2CallbackResponse, error) {
	// Get testable configuration
	testConfig := h.testableConfigProvider(isDev)

	// Exchange code for token using testable exchanger
	token, err := testConfig.OAuth2Exchanger.Exchange(ctx, r.URL.Query().Get("code"))
	if err != nil {
		return nil, ErrExchangeFailed
	}

	// Verify the ID Token and extract claims using testable verifier
	rawID, ok := token.Extra("id_token").(string)
	if !ok {
		return nil, ErrMissingIDToken
	}

	idTokenClaims, err := testConfig.IDVerifier.Verify(ctx, rawID)
	if err != nil {
		return nil, ErrInvalidIDToken
	}

	// Extract claims from ID token
	var claims map[string]interface{}
	if err := idTokenClaims.Claims(&claims); err != nil {
		return nil, ErrInvalidIDToken
	}

	// Get state cookie for validation
	st, err := r.Cookie("oauth_state")
	stateCookie := ""
	if err == nil {
		stateCookie = st.Value
	}

	// Create callback request
	callbackReq := &OAuth2CallbackRequest{
		Code:        r.URL.Query().Get("code"),
		State:       r.URL.Query().Get("state"),
		StateCookie: stateCookie,
		IsDev:       isDev,
		Claims:      claims,
		OAuthToken:  token,
	}

	return h.authService.ProcessOAuth2Callback(ctx, callbackReq)
}

// OAuth2Logout handles GET /logout requests
func (h *Handler) OAuth2Logout(w http.ResponseWriter, r *http.Request) {
	h.handleLogout(w, r)
}

var (
	// synapseOIDC is a global variable that holds the OIDC configuration
	// for the Synapse OAuth2 server.
	SynapseOIDC = OIDCConfig{
		ClientID:     os.Getenv("SYNAPSE_OIDC_CLIENT_ID"),
		ClientSecret: os.Getenv("SYNAPSE_OIDC_CLIENT_SECRET"),
		RedirectURL:  os.Getenv("SYNAPSE_OIDC_CLIENT_CALLBACK_URL"),
		IssuerURL:    os.Getenv("SYNAPSE_OIDC_ISSUER_URL"),
	}

	// synapseOIDCLocal is a local version of the OIDC configuration
	// used for development purposes, pointing to keycloak.
	SynapseOIDCLocal OIDCConfig

	SynapseOIDCScopes = []string{oidc.ScopeOpenID, "profile", "email"}

	// localhostHTTPProxy is a custom HTTP client that rewrites requests
	// to "localhost:8091" to "host.docker.internal:8091". This is necessary
	// for the OIDC provider to communicate with the host machine from within
	// a Docker container, as Docker containers cannot directly access services
	// running on the host machine using "localhost".  This is not a security
	// concern in prod, because in production, the request will simply fail
	// because there is no OIDC provider listening there.
	LocalhostHTTPProxy = &http.Client{Transport: &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			if strings.HasSuffix(addr, "localhost:8091") {
				addr = "host.docker.internal:8091"
			}
			return (&net.Dialer{}).DialContext(ctx, network, addr)
		},
	}}
)

// GetSessionStore returns the session store (for testing)
func (h *Handler) GetSessionStore() session.SessionStore {
	return h.sessionStore
}

// OIDCConfig holds the configuration for OpenID Connect (OIDC) authentication.
type OIDCConfig struct {
	ClientID     string
	ClientSecret string
	RedirectURL  string
	IssuerURL    string
	Provider     *oidc.Provider
	OAuth2Config *oauth2.Config
	Verifier     *oidc.IDTokenVerifier
	Scope        string
}

func handleLogin(w http.ResponseWriter, r *http.Request) {
	isDev := strings.HasPrefix(r.Host, "localhost:4200")
	state := util.RandomString(32)

	// Set the state cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "oauth_state",
		Value:    state,
		Path:     "/",
		HttpOnly: true,
		Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
		SameSite: http.SameSiteLaxMode,
	})

	// Choose the OIDC configuration based on the request host
	cfg := map[bool]*oauth2.Config{
		false: SynapseOIDC.OAuth2Config,
		true:  SynapseOIDCLocal.OAuth2Config,
	}[isDev]

	// Redirect to the OIDC provider's authorization endpoint
	http.Redirect(w, r, cfg.AuthCodeURL(state), http.StatusFound)
}

func (h *Handler) handleLogout(w http.ResponseWriter, r *http.Request) {
	isDev := strings.HasPrefix(r.Host, "localhost:4200")

	// Grab the session_id cookie (if any)
	cookie, err := r.Cookie("session_id")
	if err == nil {
		// Delete the server‐side session
		h.sessionStore.ClearSession(cookie.Value)
	}

	// Clear the cookie in the browser
	http.SetCookie(w, &http.Cookie{
		Name:     "session_id",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
		Expires:  time.Unix(0, 0),
		MaxAge:   -1,
		SameSite: http.SameSiteNoneMode,
	})

	// Redirect to its end-session endpoint:
	// NOTE: This would log the user out of the OIDC provider, but it is not
	// strictly necessary for our use case, since we are not using the OIDC
	// provider for anything other than authentication.  This code is left here
	// for reference, in case we want to implement a full logout flow in the
	// future.
	//
	// redirectURI := url.QueryEscape(oidcConfig.RedirectURL)
	// logoutURL := oidcConfig.IssuerURL +
	// 	"/protocol/openid-connect/logout?redirect_uri=" + redirectURI
	// http.Redirect(w, r, logoutURL, http.StatusFound)

	http.Redirect(w, r, "/", http.StatusFound)
}

// createSessionWithCookie creates a session with permissions and sets the session cookie
func (h *Handler) createSessionWithCookie(ctx context.Context, w http.ResponseWriter, userID string, oauthToken *oauth2.Token, isDev bool) (string, error) {
	// Get database connections for permissions
	connections, err := connect.GetConnections(ctx)
	var userPermissions *authorizer.UserPermissions
	if err == nil {
		// Get user permissions
		userPermissions, err = authorizer.GetUserPermissions(connections.Postgres, userID)
	}
	if err != nil {
		logger.Warnf("Failed to load user permissions for %s: %v", userID, err)
		// Continue without permissions rather than failing the login
		userPermissions = &authorizer.UserPermissions{
			UserID:      userID,
			Permissions: []authorizer.Permission{},
		}
	}
	// Create a session with comprehensive session data
	sessionID := util.RandomString(32)
	sessionData := &session.SessionData{
		UserID:          userID,
		OAuthToken:      oauthToken,
		UserPermissions: userPermissions,
		Data:            make(map[string]any),
	}
	h.sessionStore.SetSession(sessionID, sessionData)

	http.SetCookie(w, &http.Cookie{
		Name:     "session_id",
		Value:    sessionID,
		Path:     "/",
		HttpOnly: true,
		Secure:   !isDev,
		SameSite: http.SameSiteStrictMode,
	})

	return sessionID, nil
}
