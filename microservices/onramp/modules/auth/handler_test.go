package auth

import (
	"context"
	"net/http"
	"net/http/httptest"
	"reflect"
	"runtime"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"golang.org/x/oauth2"

	"synapse-its.com/onramp/domain"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/rest/onramp/session"
)

// setupOIDCEnv sets up the OIDC environment variables for testing
func setupOIDCEnv(t *testing.T) {
	t.Setenv("SYNAPSE_OIDC_CLIENT_ID", "test-client-id")
	t.Setenv("SYNAPSE_OIDC_CLIENT_SECRET", "test-client-secret")
	t.Setenv("SYNAPSE_OIDC_CLIENT_CALLBACK_URL", "http://localhost:4200/callback")
	t.Setenv("SYNAPSE_OIDC_ISSUER_URL", "http://localhost:8091/auth/realms/test")
}

// setupTestOAuth2Config initializes the OAuth2 configuration for testing
func setupTestOAuth2Config(t *testing.T) {
	// Create a mock OAuth2 config for testing
	mockConfig := &oauth2.Config{
		ClientID:     "test-client-id",
		ClientSecret: "test-client-secret",
		RedirectURL:  "http://localhost:4200/callback",
		Endpoint: oauth2.Endpoint{
			AuthURL:  "http://localhost:8091/auth/realms/test/protocol/openid-connect/auth",
			TokenURL: "http://localhost:8091/auth/realms/test/protocol/openid-connect/token",
		},
		Scopes: SynapseOIDCScopes,
	}

	// Set up both production and local configs for testing
	SynapseOIDC.OAuth2Config = mockConfig
	SynapseOIDCLocal.OAuth2Config = mockConfig
}

// setupMockOAuth2Config creates a mock OAuth2 config that returns predefined tokens
func setupMockOAuth2Config(t *testing.T, token *oauth2.Token, mockError error) {
	// Create a mock OAuth2 config that returns the provided token
	mockConfig := &oauth2.Config{
		ClientID:     "test-client-id",
		ClientSecret: "test-client-secret",
		RedirectURL:  "http://localhost:4200/callback",
		Endpoint: oauth2.Endpoint{
			AuthURL:  "http://localhost:8091/auth/realms/test/protocol/openid-connect/auth",
			TokenURL: "http://localhost:8091/auth/realms/test/protocol/openid-connect/token",
		},
		Scopes: SynapseOIDCScopes,
	}

	// Set up both production and local configs for testing
	SynapseOIDC.OAuth2Config = mockConfig
	SynapseOIDCLocal.OAuth2Config = mockConfig
}

// setupMockConnections creates mock connections and adds them to the context
func setupMockConnections(ctx context.Context) context.Context {
	mockConns := mocks.FakeConns()
	return context.WithValue(ctx, connect.ConnectionsKey, mockConns)
}

// setupMockUserPermissions sets up the mock database to return user permissions
func setupMockUserPermissions(mockDB *mocks.FakeDBExecutor, userID string) {
	mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
		// Cast dest to the expected slice type and populate with sample data
		if slice, ok := dest.(*[]authorizer.Permission); ok {
			*slice = []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "550e8400-e29b-41d4-a716-446655440001",
					OrganizationID: "550e8400-e29b-41d4-a716-446655440001",
					Permissions:    []string{"org_view_users", "org_manage_users"},
				},
				{
					Scope:          "device_group",
					ScopeID:        "550e8400-e29b-41d4-a716-446655440002",
					OrganizationID: "550e8400-e29b-41d4-a716-446655440001",
					Permissions:    []string{"device_group_manage_devices"},
				},
			}
		}
		return nil
	}
}

func TestHandleLogin(t *testing.T) {
	// Setup test environment variables for OIDC
	setupOIDCEnv(t)

	// Setup test OAuth2 configuration
	setupTestOAuth2Config(t)

	tests := []struct {
		name     string
		host     string
		wantCode int
	}{
		{
			name:     "localhost dev environment",
			host:     "localhost:4200",
			wantCode: http.StatusFound,
		},
		{
			name:     "production environment",
			host:     "example.com",
			wantCode: http.StatusFound,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/login", nil)
			req.Host = tc.host
			rr := httptest.NewRecorder()

			handler := NewHandler(&MockAuthService{})
			handler.OAuth2Login(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code)

			// Check that state cookie was set
			cookies := rr.Result().Cookies()
			var stateCookie *http.Cookie
			for _, cookie := range cookies {
				if cookie.Name == "oauth_state" {
					stateCookie = cookie
					break
				}
			}
			assert.NotNil(t, stateCookie, "oauth_state cookie should be set")
			assert.NotEmpty(t, stateCookie.Value, "oauth_state cookie should have a value")

			// Check redirect location contains expected parameters
			location := rr.Header().Get("Location")
			assert.Contains(t, location, "state=", "redirect should contain state parameter")
		})
	}
}

func TestHandleCallback(t *testing.T) {
	// Setup test environment variables for OIDC
	setupOIDCEnv(t)

	// Setup test OAuth2 configuration
	setupTestOAuth2Config(t)

	tests := []struct {
		name     string
		host     string
		state    string
		code     string
		cookie   *http.Cookie
		wantCode int
	}{
		{
			name:     "missing state cookie",
			host:     "localhost:4200",
			state:    "test-state",
			code:     "test-code",
			cookie:   nil,
			wantCode: http.StatusBadRequest,
		},
		{
			name:     "invalid state",
			host:     "localhost:4200",
			state:    "wrong-state",
			code:     "test-code",
			cookie:   &http.Cookie{Name: "oauth_state", Value: "correct-state"},
			wantCode: http.StatusBadRequest,
		},
		{
			name:     "missing code parameter",
			host:     "localhost:4200",
			state:    "test-state",
			code:     "",
			cookie:   &http.Cookie{Name: "oauth_state", Value: "test-state"},
			wantCode: http.StatusInternalServerError, // OAuth2 exchange will fail
		},
		{
			name:     "production host with invalid code",
			host:     "example.com",
			state:    "test-state",
			code:     "invalid-code",
			cookie:   &http.Cookie{Name: "oauth_state", Value: "test-state"},
			wantCode: http.StatusInternalServerError, // OAuth2 exchange will fail
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/callback?state="+tc.state+"&code="+tc.code, nil)
			req.Host = tc.host
			if tc.cookie != nil {
				req.AddCookie(tc.cookie)
			}
			rr := httptest.NewRecorder()

			handler := NewHandler(&MockAuthService{})
			handler.OAuth2Callback(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code)
		})
	}
}

func TestHandleLogout(t *testing.T) {
	// Setup test environment variables for OIDC
	setupOIDCEnv(t)

	// Create handler with its own session store
	handler := NewHandler(&MockAuthService{})

	// Pre-populate session store using proper interface
	validToken := &oauth2.Token{AccessToken: "test-access-token"}
	handler.GetSessionStore().SetSession("test-session-id", &session.SessionData{
		UserID:          "test-user",
		OAuthToken:      validToken,
		UserPermissions: nil,
		Data:            make(map[string]any),
	})

	// Setup test OAuth2 configuration
	setupTestOAuth2Config(t)

	tests := []struct {
		name          string
		host          string
		sessionCookie *http.Cookie
		wantCode      int
	}{
		{
			name:          "logout with valid session - localhost",
			host:          "localhost:4200",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "test-session-id"},
			wantCode:      http.StatusFound,
		},
		{
			name:          "logout with valid session - production",
			host:          "example.com",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "test-session-id"},
			wantCode:      http.StatusFound,
		},
		{
			name:          "logout without session cookie",
			host:          "localhost:4200",
			sessionCookie: nil,
			wantCode:      http.StatusFound,
		},
		{
			name:          "logout with onramp host",
			host:          "onramp:4200",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "test-session-id"},
			wantCode:      http.StatusFound,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/logout", nil)
			req.Host = tc.host
			if tc.sessionCookie != nil {
				req.AddCookie(tc.sessionCookie)
			}
			rr := httptest.NewRecorder()

			handler.OAuth2Logout(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code)

			// Check that session cookie is cleared
			cookies := rr.Result().Cookies()
			var sessionCookie *http.Cookie
			for _, cookie := range cookies {
				if cookie.Name == "session_id" {
					sessionCookie = cookie
					break
				}
			}
			if sessionCookie != nil {
				assert.Empty(t, sessionCookie.Value, "session cookie should be cleared")
				assert.True(t, sessionCookie.MaxAge < 0, "session cookie should be expired")
			}
		})
	}
}

// MockAuthService is a mock implementation of AuthService for testing
type MockAuthService struct {
	BasicAuthFunc             func(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error)
	RegisterFunc              func(ctx context.Context, req *RegisterRequest) error
	HandleOIDCLoginFunc       func(ctx context.Context, req *OIDCLoginRequest) (*LoginResponse, error)
	ProcessOAuth2CallbackFunc func(ctx context.Context, req *OAuth2CallbackRequest) (*OAuth2CallbackResponse, error)
}

func (m *MockAuthService) BasicAuth(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) {
	if m.BasicAuthFunc != nil {
		return m.BasicAuthFunc(ctx, req)
	}
	return nil, nil
}

func (m *MockAuthService) Register(ctx context.Context, req *RegisterRequest) error {
	if m.RegisterFunc != nil {
		return m.RegisterFunc(ctx, req)
	}
	return nil
}

func (m *MockAuthService) HandleOIDCLogin(ctx context.Context, req *OIDCLoginRequest) (*LoginResponse, error) {
	if m.HandleOIDCLoginFunc != nil {
		return m.HandleOIDCLoginFunc(ctx, req)
	}
	return nil, nil
}

func (m *MockAuthService) ProcessOAuth2Callback(ctx context.Context, req *OAuth2CallbackRequest) (*OAuth2CallbackResponse, error) {
	if m.ProcessOAuth2CallbackFunc != nil {
		return m.ProcessOAuth2CallbackFunc(ctx, req)
	}
	return nil, nil
}

func TestHandler_BasicAuth(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		method       string
		formData     map[string]string
		mockResponse *LoginResponse
		mockError    error
		expectedCode int
		expectedBody string
		setupMock    func(*MockAuthService)
		setupContext func(context.Context) context.Context
	}{
		{
			name:     "successful authentication",
			method:   http.MethodPost,
			formData: map[string]string{"username": "<EMAIL>", "password": "puppies1234"},
			setupMock: func(m *MockAuthService) {
				userID := uuid.MustParse("063f2596-9dea-57f8-84aa-5d693c59f4e4")
				m.BasicAuthFunc = func(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) {
					return &LoginResponse{
						User:  &domain.User{ID: userID, FirstName: "Test1", LastName: "User", Mobile: "******-555-1234", IanaTimezone: "America/Chicago", Description: "Secondary test user"},
						Token: "test-token",
					}, nil
				}
			},
			setupContext: func(ctx context.Context) context.Context {
				return setupMockConnections(ctx)
			},
			expectedCode: http.StatusOK,
			expectedBody: `{"code":200,"data":{"user":{"id":"063f2596-9dea-57f8-84aa-5d693c59f4e4","firstName":"Test1","lastName":"User","mobile":"******-555-1234","ianaTimezone":"America/Chicago","description":"Secondary test user"},"token":"test-token"},"message":"Request Succeeded","status":"success"}`,
		},
		{
			name:     "wrong method",
			method:   http.MethodGet,
			formData: map[string]string{"username": "testuser", "password": "testpass"},
			setupMock: func(m *MockAuthService) {
				m.BasicAuthFunc = func(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) {
					return nil, nil
				}
			},
			expectedCode: http.StatusMethodNotAllowed,
			expectedBody: `{"code":405,"data":null,"message":"Method Not Allowed","status":"error"}`,
		},
		{
			name:     "authentication failure",
			method:   http.MethodPost,
			formData: map[string]string{"username": "testuser", "password": "wrongpass"},
			setupMock: func(m *MockAuthService) {
				m.BasicAuthFunc = func(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) {
					return nil, domain.ErrInvalidCredentials
				}
			},
			expectedCode: http.StatusUnauthorized,
			expectedBody: `{"code":401,"data":null,"message":"Unauthorized","status":"error"}`,
		},
		{
			name:     "empty username",
			method:   http.MethodPost,
			formData: map[string]string{"username": "", "password": "testpass"},
			setupMock: func(m *MockAuthService) {
				m.BasicAuthFunc = func(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) {
					return nil, domain.ErrInvalidCredentials
				}
			},
			expectedCode: http.StatusUnauthorized,
			expectedBody: `{"code":401,"data":null,"message":"Unauthorized","status":"error"}`,
		},
		{
			name:     "empty password",
			method:   http.MethodPost,
			formData: map[string]string{"username": "testuser", "password": ""},
			setupMock: func(m *MockAuthService) {
				m.BasicAuthFunc = func(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) {
					return nil, domain.ErrInvalidCredentials
				}
			},
			expectedCode: http.StatusUnauthorized,
			expectedBody: `{"code":401,"data":null,"message":"Unauthorized","status":"error"}`,
		},
		{
			name:     "missing form data",
			method:   http.MethodPost,
			formData: map[string]string{},
			setupMock: func(m *MockAuthService) {
				userID := uuid.MustParse("45627c04-8d87-595a-a31b-2e675e22417a")
				m.BasicAuthFunc = func(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) {
					return &LoginResponse{User: &domain.User{ID: userID}, Token: ""}, nil
				}
			},
			setupContext: func(ctx context.Context) context.Context {
				return setupMockConnections(ctx)
			},
			expectedCode: http.StatusOK,
			expectedBody: `{"code":200,"data":{"user":{"id":"45627c04-8d87-595a-a31b-2e675e22417a","firstName":"","lastName":"","mobile":"","ianaTimezone":"","description":""},"token":""},"message":"Request Succeeded","status":"success"}`,
		},
		{
			name:         "form parse error",
			method:       http.MethodPost,
			formData:     nil, // not used
			setupMock:    nil, // not needed
			expectedCode: http.StatusBadRequest,
			expectedBody: `{"code":400,"data":null,"message":"Bad Request","status":"error"}`,
		},
		{
			name:     "database connection error - continues with empty permissions",
			method:   http.MethodPost,
			formData: map[string]string{"username": "testuser", "password": "testpass"},
			setupMock: func(m *MockAuthService) {
				userID := uuid.MustParse("063f2596-9dea-57f8-84aa-5d693c59f4e4")
				m.BasicAuthFunc = func(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) {
					return &LoginResponse{
						User:  &domain.User{ID: userID, FirstName: "Test", LastName: "User"},
						Token: "test-token",
					}, nil
				}
			},
			setupContext: func(ctx context.Context) context.Context {
				// Don't add connections to context to simulate connection error
				return ctx
			},
			expectedCode: http.StatusOK,
			expectedBody: `{"code":200,"data":{"user":{"id":"063f2596-9dea-57f8-84aa-5d693c59f4e4","firstName":"Test","lastName":"User","mobile":"","ianaTimezone":"","description":""},"token":"test-token"},"message":"Request Succeeded","status":"success"}`,
		},
		{
			name:     "user permissions error - continues with empty permissions",
			method:   http.MethodPost,
			formData: map[string]string{"username": "testuser", "password": "testpass"},
			setupMock: func(m *MockAuthService) {
				userID := uuid.MustParse("063f2596-9dea-57f8-84aa-5d693c59f4e4")
				m.BasicAuthFunc = func(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) {
					return &LoginResponse{
						User:  &domain.User{ID: userID, FirstName: "Test", LastName: "User"},
						Token: "test-token",
					}, nil
				}
			},
			setupContext: func(ctx context.Context) context.Context {
				mockConns := mocks.FakeConns()
				// Set up mock DB to return error for user permissions
				mockDB := mockConns.Postgres.(*mocks.FakeDBExecutor)
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return assert.AnError
				}
				return context.WithValue(ctx, connect.ConnectionsKey, mockConns)
			},
			expectedCode: http.StatusOK,
			expectedBody: `{"code":200,"data":{"user":{"id":"063f2596-9dea-57f8-84aa-5d693c59f4e4","firstName":"Test","lastName":"User","mobile":"","ianaTimezone":"","description":""},"token":"test-token"},"message":"Request Succeeded","status":"success"}`,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Create mock service
			mockService := &MockAuthService{}
			if tc.setupMock != nil {
				tc.setupMock(mockService)
			}

			// Create handler with mock service
			handler := NewHandler(mockService)

			// Create request
			var req *http.Request
			if tc.name == "form parse error" {
				// Send invalid form body
				req = httptest.NewRequest(tc.method, "/login", strings.NewReader("%%%"))
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
			} else if tc.method == http.MethodPost && tc.formData != nil && len(tc.formData) > 0 {
				req = httptest.NewRequest(tc.method, "/login", nil)
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
				form := req.PostForm
				if form == nil {
					form = make(map[string][]string)
				}
				for key, value := range tc.formData {
					form[key] = []string{value}
				}
				req.PostForm = form
			} else {
				req = httptest.NewRequest(tc.method, "/login", nil)
			}

			// Setup context if needed
			if tc.setupContext != nil {
				req = req.WithContext(tc.setupContext(req.Context()))
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call the handler
			handler.BasicAuth(rr, req)

			// Assert status code
			assert.Equal(t, tc.expectedCode, rr.Code)

			// Assert response body
			body := strings.TrimSpace(rr.Body.String())
			assert.Equal(t, tc.expectedBody, body)
		})
	}
}

func TestHandler_Register(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		method       string
		formData     map[string]string
		mockError    error
		expectedCode int
		expectedBody string
		setupMock    func(*MockAuthService)
	}{
		{
			name:   "successful registration",
			method: http.MethodPost,
			formData: map[string]string{
				"firstname": "John",
				"lastname":  "Doe",
				"username":  "johndoe",
				"password":  "password123",
				"email":     "<EMAIL>",
			},
			setupMock: func(m *MockAuthService) {
				m.RegisterFunc = func(ctx context.Context, req *RegisterRequest) error {
					return nil
				}
			},
			expectedCode: http.StatusOK,
			expectedBody: `{"code":200,"data":null,"message":"Request Succeeded","status":"success"}`,
		},
		{
			name:   "wrong method",
			method: http.MethodGet,
			formData: map[string]string{
				"firstname": "John",
				"lastname":  "Doe",
				"username":  "johndoe",
				"password":  "password123",
				"email":     "<EMAIL>",
			},
			setupMock: func(m *MockAuthService) {
				m.RegisterFunc = func(ctx context.Context, req *RegisterRequest) error {
					return nil
				}
			},
			expectedCode: http.StatusMethodNotAllowed,
			expectedBody: `{"code":405,"data":null,"message":"Method Not Allowed","status":"error"}`,
		},
		{
			name:   "registration failure - user already exists",
			method: http.MethodPost,
			formData: map[string]string{
				"firstname": "John",
				"lastname":  "Doe",
				"username":  "existinguser",
				"password":  "password123",
				"email":     "<EMAIL>",
			},
			setupMock: func(m *MockAuthService) {
				m.RegisterFunc = func(ctx context.Context, req *RegisterRequest) error {
					return domain.ErrUserAlreadyExists
				}
			},
			expectedCode: http.StatusInternalServerError,
			expectedBody: `{"code":500,"data":null,"message":"Internal Server Error","status":"error"}`,
		},
		{
			name:   "registration failure - invalid input",
			method: http.MethodPost,
			formData: map[string]string{
				"firstname": "John",
				"lastname":  "Doe",
				"username":  "",
				"password":  "password123",
				"email":     "<EMAIL>",
			},
			setupMock: func(m *MockAuthService) {
				m.RegisterFunc = func(ctx context.Context, req *RegisterRequest) error {
					return domain.ErrInvalidInput
				}
			},
			expectedCode: http.StatusInternalServerError,
			expectedBody: `{"code":500,"data":null,"message":"Internal Server Error","status":"error"}`,
		},
		{
			name:     "missing form data",
			method:   http.MethodPost,
			formData: map[string]string{},
			setupMock: func(m *MockAuthService) {
				m.RegisterFunc = func(ctx context.Context, req *RegisterRequest) error {
					return nil
				}
			},
			expectedCode: http.StatusOK,
			expectedBody: `{"code":200,"data":null,"message":"Request Succeeded","status":"success"}`,
		},
		{
			name:   "partial form data",
			method: http.MethodPost,
			formData: map[string]string{
				"firstname": "John",
				"lastname":  "Doe",
				// missing username, password, email
			},
			setupMock: func(m *MockAuthService) {
				m.RegisterFunc = func(ctx context.Context, req *RegisterRequest) error {
					return nil
				}
			},
			expectedCode: http.StatusOK,
			expectedBody: `{"code":200,"data":null,"message":"Request Succeeded","status":"success"}`,
		},
		{
			name:         "form parse error",
			method:       http.MethodPost,
			formData:     nil, // not used
			setupMock:    nil, // not needed
			expectedCode: http.StatusBadRequest,
			expectedBody: `{"code":400,"data":null,"message":"Bad Request","status":"error"}`,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Create mock service
			mockService := &MockAuthService{}
			if tc.setupMock != nil {
				tc.setupMock(mockService)
			}

			// Create handler with mock service
			handler := NewHandler(mockService)

			// Create request
			var req *http.Request
			if tc.name == "form parse error" {
				// Send invalid form body
				req = httptest.NewRequest(tc.method, "/register", strings.NewReader("%%%"))
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
			} else if tc.method == http.MethodPost && tc.formData != nil && len(tc.formData) > 0 {
				req = httptest.NewRequest(tc.method, "/register", nil)
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
				form := req.PostForm
				if form == nil {
					form = make(map[string][]string)
				}
				for key, value := range tc.formData {
					form[key] = []string{value}
				}
				req.PostForm = form
			} else {
				req = httptest.NewRequest(tc.method, "/register", nil)
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call the handler
			handler.Register(rr, req)

			// Assert status code
			assert.Equal(t, tc.expectedCode, rr.Code)

			// Assert response body
			body := strings.TrimSpace(rr.Body.String())
			assert.Equal(t, tc.expectedBody, body)
		})
	}
}

func TestHandler_RegisterRoutes(t *testing.T) {
	t.Parallel()

	h := NewHandler(&MockAuthService{})
	r := mux.NewRouter()

	h.RegisterRoutes(r)

	type routeInfo struct {
		path        string
		method      string
		handlerName string
	}

	expected := []routeInfo{
		{"/login", http.MethodPost, "BasicAuth"},
		{"/register", http.MethodPost, "Register"},
		{"/login", http.MethodGet, "OAuth2Login"},
		{"/callback", http.MethodGet, "OAuth2Callback"},
		{"/logout", http.MethodGet, "OAuth2Logout"},
	}

	var actual []routeInfo
	err := r.Walk(func(route *mux.Route, router *mux.Router, ancestors []*mux.Route) error {
		path, _ := route.GetPathTemplate()
		methods, _ := route.GetMethods()
		handler := route.GetHandler()
		handlerName := ""
		if handler != nil {
			handlerName = runtime.FuncForPC(reflect.ValueOf(handler).Pointer()).Name()
			// Extract only the method name
			if idx := strings.LastIndex(handlerName, "."); idx != -1 {
				handlerName = handlerName[idx+1:]
			}
		}
		for _, m := range methods {
			actual = append(actual, routeInfo{path, m, handlerName})
		}
		return nil
	})
	if err != nil {
		t.Fatalf("router walk failed: %v", err)
	}

	for _, exp := range expected {
		found := false
		for _, act := range actual {
			if exp.path == act.path && exp.method == act.method && strings.HasPrefix(act.handlerName, exp.handlerName) {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected route %s %s -> %s not found. Actual: %+v", exp.method, exp.path, exp.handlerName, actual)
		}
	}
}

func TestHandler_GetSessionStore(t *testing.T) {
	t.Parallel()

	handler := NewHandler(&MockAuthService{})
	sessionStore := handler.GetSessionStore()

	assert.NotNil(t, sessionStore, "Session store should not be nil")
}

func TestNewHandler(t *testing.T) {
	t.Parallel()

	mockService := &MockAuthService{}
	handler := NewHandler(mockService)

	assert.NotNil(t, handler, "Handler should not be nil")
	assert.Equal(t, mockService, handler.authService, "Auth service should be set correctly")
	assert.NotNil(t, handler.sessionStore, "Session store should be initialized")
}

// TestCreateSessionWithCookie tests the createSessionWithCookie function
func TestCreateSessionWithCookie(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		userID         string
		oauthToken     *oauth2.Token
		isDev          bool
		setupContext   func(context.Context) context.Context
		expectedError  bool
		expectedResult string
	}{
		{
			name:       "successful session creation with permissions - dev environment",
			userID:     "test-user-id",
			oauthToken: &oauth2.Token{AccessToken: "test-token"},
			isDev:      true,
			setupContext: func(ctx context.Context) context.Context {
				mockConns := mocks.FakeConns()
				setupMockUserPermissions(mockConns.Postgres.(*mocks.FakeDBExecutor), "test-user-id")
				return context.WithValue(ctx, connect.ConnectionsKey, mockConns)
			},
			expectedError:  false,
			expectedResult: "", // Will be generated
		},
		{
			name:       "successful session creation with permissions - production environment",
			userID:     "test-user-id",
			oauthToken: &oauth2.Token{AccessToken: "test-token"},
			isDev:      false,
			setupContext: func(ctx context.Context) context.Context {
				mockConns := mocks.FakeConns()
				setupMockUserPermissions(mockConns.Postgres.(*mocks.FakeDBExecutor), "test-user-id")
				return context.WithValue(ctx, connect.ConnectionsKey, mockConns)
			},
			expectedError:  false,
			expectedResult: "", // Will be generated
		},
		{
			name:       "session creation without database connections - continues with empty permissions",
			userID:     "test-user-id",
			oauthToken: &oauth2.Token{AccessToken: "test-token"},
			isDev:      true,
			setupContext: func(ctx context.Context) context.Context {
				// Don't add connections to context to simulate connection error
				return ctx
			},
			expectedError:  false,
			expectedResult: "", // Will be generated
		},
		{
			name:       "session creation with user permissions error - continues with empty permissions",
			userID:     "test-user-id",
			oauthToken: &oauth2.Token{AccessToken: "test-token"},
			isDev:      true,
			setupContext: func(ctx context.Context) context.Context {
				mockConns := mocks.FakeConns()
				// Set up mock DB to return error for user permissions
				mockDB := mockConns.Postgres.(*mocks.FakeDBExecutor)
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return assert.AnError
				}
				return context.WithValue(ctx, connect.ConnectionsKey, mockConns)
			},
			expectedError:  false,
			expectedResult: "", // Will be generated
		},
		{
			name:       "session creation with nil oauth token",
			userID:     "test-user-id",
			oauthToken: nil,
			isDev:      true,
			setupContext: func(ctx context.Context) context.Context {
				mockConns := mocks.FakeConns()
				setupMockUserPermissions(mockConns.Postgres.(*mocks.FakeDBExecutor), "test-user-id")
				return context.WithValue(ctx, connect.ConnectionsKey, mockConns)
			},
			expectedError:  false,
			expectedResult: "", // Will be generated
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Create handler
			handler := NewHandler(&MockAuthService{})

			// Create request and response recorder
			req := httptest.NewRequest("GET", "/test", nil)
			if tc.setupContext != nil {
				req = req.WithContext(tc.setupContext(req.Context()))
			}
			rr := httptest.NewRecorder()

			// Call the function
			sessionID, err := handler.createSessionWithCookie(req.Context(), rr, tc.userID, tc.oauthToken, tc.isDev)

			// Assert results
			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, sessionID, "Session ID should be generated")
				assert.Len(t, sessionID, 32, "Session ID should be 32 characters")

				// Check that session was created in store
				sessionData, exists := handler.sessionStore.GetSession(sessionID)
				assert.True(t, exists, "Session should exist in store")
				assert.Equal(t, tc.userID, sessionData.UserID, "User ID should match")
				assert.Equal(t, tc.oauthToken, sessionData.OAuthToken, "OAuth token should match")

				// Check that cookie was set
				cookies := rr.Result().Cookies()
				var sessionCookie *http.Cookie
				for _, cookie := range cookies {
					if cookie.Name == "session_id" {
						sessionCookie = cookie
						break
					}
				}
				assert.NotNil(t, sessionCookie, "Session cookie should be set")
				assert.Equal(t, sessionID, sessionCookie.Value, "Cookie value should match session ID")
				assert.Equal(t, "/", sessionCookie.Path, "Cookie path should be /")
				assert.True(t, sessionCookie.HttpOnly, "Cookie should be HttpOnly")
				assert.Equal(t, !tc.isDev, sessionCookie.Secure, "Cookie Secure should match environment")
				assert.Equal(t, http.SameSiteStrictMode, sessionCookie.SameSite, "Cookie SameSite should be Strict")
			}
		})
	}
}

// TestProcessOAuth2CallbackLogic tests the processOAuth2CallbackLogic function
func TestProcessOAuth2CallbackLogic(t *testing.T) {
	// Setup test environment variables for OIDC
	setupOIDCEnv(t)

	// Setup test OAuth2 configuration
	setupTestOAuth2Config(t)

	tests := []struct {
		name           string
		host           string
		code           string
		state          string
		stateCookie    string
		setupMock      func(*MockAuthService)
		expectedError  error
		expectedResult *OAuth2CallbackResponse
	}{
		{
			name:        "OAuth2 exchange failure - invalid code",
			host:        "localhost:4200",
			code:        "invalid-code",
			state:       "valid-state",
			stateCookie: "valid-state",
			setupMock: func(m *MockAuthService) {
				// No mock needed as exchange will fail
			},
			expectedError:  ErrExchangeFailed,
			expectedResult: nil,
		},
		{
			name:        "OAuth2 exchange failure - production environment",
			host:        "example.com",
			code:        "invalid-code",
			state:       "valid-state",
			stateCookie: "valid-state",
			setupMock: func(m *MockAuthService) {
				// No mock needed as exchange will fail
			},
			expectedError:  ErrExchangeFailed,
			expectedResult: nil,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create handler with mock service
			mockService := &MockAuthService{}
			if tc.setupMock != nil {
				tc.setupMock(mockService)
			}
			handler := NewHandler(mockService)

			// Create request with query parameters
			req := httptest.NewRequest("GET", "/callback?code="+tc.code+"&state="+tc.state, nil)
			req.Host = tc.host

			// Add state cookie if provided
			if tc.stateCookie != "" {
				req.AddCookie(&http.Cookie{
					Name:  "oauth_state",
					Value: tc.stateCookie,
				})
			}

			// Create context
			ctx := req.Context()
			isDev := strings.HasPrefix(tc.host, "localhost:4200")

			// Call the function
			result, err := handler.processOAuth2CallbackLogic(ctx, req, isDev)

			// Assert results
			if tc.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tc.expectedError)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if tc.expectedResult != nil {
					assert.Equal(t, tc.expectedResult.User.ID, result.User.ID)
					assert.Equal(t, tc.expectedResult.Token, result.Token)
					assert.Equal(t, tc.expectedResult.OAuthToken.AccessToken, result.OAuthToken.AccessToken)
				}
			}
		})
	}
}

// TestOAuth2Callback_AdditionalErrorPaths tests additional error paths in OAuth2Callback
func TestOAuth2Callback_AdditionalErrorPaths(t *testing.T) {
	// Setup test environment variables for OIDC
	setupOIDCEnv(t)

	// Setup test OAuth2 configuration
	setupTestOAuth2Config(t)

	tests := []struct {
		name         string
		host         string
		code         string
		state        string
		stateCookie  string
		setupMock    func(*MockAuthService)
		expectedCode int
		expectedBody string
	}{
		{
			name:        "processOAuth2CallbackLogic error",
			host:        "localhost:4200",
			code:        "invalid-code",
			state:       "valid-state",
			stateCookie: "valid-state",
			setupMock: func(m *MockAuthService) {
				// No mock needed as exchange will fail
			},
			expectedCode: http.StatusInternalServerError,
			expectedBody: `{"code":500,"data":null,"message":"Internal Server Error","status":"error"}`,
		},
		{
			name:        "createSessionWithCookie error",
			host:        "localhost:4200",
			code:        "valid-code",
			state:       "valid-state",
			stateCookie: "valid-state",
			setupMock: func(m *MockAuthService) {
				userID := uuid.MustParse("063f2596-9dea-57f8-84aa-5d693c59f4e4")
				m.ProcessOAuth2CallbackFunc = func(ctx context.Context, req *OAuth2CallbackRequest) (*OAuth2CallbackResponse, error) {
					return &OAuth2CallbackResponse{
						User: &domain.User{
							ID:        userID,
							FirstName: "Test",
							LastName:  "User",
						},
						Token: "test-token",
						OAuthToken: &oauth2.Token{
							AccessToken: "test-oauth-token",
						},
					}, nil
				}
			},
			expectedCode: http.StatusInternalServerError,
			expectedBody: `{"code":500,"data":null,"message":"Internal Server Error","status":"error"}`,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create handler with mock service
			mockService := &MockAuthService{}
			if tc.setupMock != nil {
				tc.setupMock(mockService)
			}
			handler := NewHandler(mockService)

			// Create request with query parameters
			req := httptest.NewRequest("GET", "/callback?code="+tc.code+"&state="+tc.state, nil)
			req.Host = tc.host

			// Add state cookie if provided
			if tc.stateCookie != "" {
				req.AddCookie(&http.Cookie{
					Name:  "oauth_state",
					Value: tc.stateCookie,
				})
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call the handler
			handler.OAuth2Callback(rr, req)

			// Assert status code
			assert.Equal(t, tc.expectedCode, rr.Code)

			// Assert response body
			body := strings.TrimSpace(rr.Body.String())
			assert.Equal(t, tc.expectedBody, body)
		})
	}
}

// TestBasicAuth_AdditionalErrorPaths tests additional error paths in BasicAuth
func TestBasicAuth_AdditionalErrorPaths(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		method       string
		formData     map[string]string
		setupMock    func(*MockAuthService)
		setupContext func(context.Context) context.Context
		expectedCode int
		expectedBody string
	}{
		{
			name:     "createSessionWithCookie error",
			method:   http.MethodPost,
			formData: map[string]string{"username": "testuser", "password": "testpass"},
			setupMock: func(m *MockAuthService) {
				userID := uuid.MustParse("063f2596-9dea-57f8-84aa-5d693c59f4e4")
				m.BasicAuthFunc = func(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) {
					return &LoginResponse{
						User:  &domain.User{ID: userID, FirstName: "Test", LastName: "User"},
						Token: "test-token",
					}, nil
				}
			},
			setupContext: func(ctx context.Context) context.Context {
				// Don't add connections to context to simulate connection error
				return ctx
			},
			expectedCode: http.StatusOK, // Should still succeed as createSessionWithCookie handles errors gracefully
			expectedBody: `{"code":200,"data":{"user":{"id":"063f2596-9dea-57f8-84aa-5d693c59f4e4","firstName":"Test","lastName":"User","mobile":"","ianaTimezone":"","description":""},"token":"test-token"},"message":"Request Succeeded","status":"success"}`,
		},
		{
			name:     "createSessionWithCookie error - should return internal error",
			method:   http.MethodPost,
			formData: map[string]string{"username": "testuser", "password": "testpass"},
			setupMock: func(m *MockAuthService) {
				userID := uuid.MustParse("063f2596-9dea-57f8-84aa-5d693c59f4e4")
				m.BasicAuthFunc = func(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) {
					return &LoginResponse{
						User:  &domain.User{ID: userID, FirstName: "Test", LastName: "User"},
						Token: "test-token",
					}, nil
				}
			},
			setupContext: func(ctx context.Context) context.Context {
				// Don't add connections to context to simulate connection error
				return ctx
			},
			expectedCode: http.StatusOK, // Should still succeed as createSessionWithCookie handles errors gracefully
			expectedBody: `{"code":200,"data":{"user":{"id":"063f2596-9dea-57f8-84aa-5d693c59f4e4","firstName":"Test","lastName":"User","mobile":"","ianaTimezone":"","description":""},"token":"test-token"},"message":"Request Succeeded","status":"success"}`,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Create mock service
			mockService := &MockAuthService{}
			if tc.setupMock != nil {
				tc.setupMock(mockService)
			}

			// Create handler with mock service
			handler := NewHandler(mockService)

			// Create request
			var req *http.Request
			if tc.method == http.MethodPost && tc.formData != nil && len(tc.formData) > 0 {
				req = httptest.NewRequest(tc.method, "/login", nil)
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
				form := req.PostForm
				if form == nil {
					form = make(map[string][]string)
				}
				for key, value := range tc.formData {
					form[key] = []string{value}
				}
				req.PostForm = form
			} else {
				req = httptest.NewRequest(tc.method, "/login", nil)
			}

			// Setup context if needed
			if tc.setupContext != nil {
				req = req.WithContext(tc.setupContext(req.Context()))
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call the handler
			handler.BasicAuth(rr, req)

			// Assert status code
			assert.Equal(t, tc.expectedCode, rr.Code)

			// Assert response body
			body := strings.TrimSpace(rr.Body.String())
			assert.Equal(t, tc.expectedBody, body)
		})
	}
}

// TestOAuth2Callback_CreateSessionError tests the error path when createSessionWithCookie fails
func TestOAuth2Callback_CreateSessionError(t *testing.T) {
	// Setup test environment variables for OIDC
	setupOIDCEnv(t)

	// Setup test OAuth2 configuration
	setupTestOAuth2Config(t)

	tests := []struct {
		name         string
		host         string
		code         string
		state        string
		stateCookie  string
		setupMock    func(*MockAuthService)
		expectedCode int
		expectedBody string
	}{
		{
			name:        "createSessionWithCookie error in OAuth2Callback",
			host:        "localhost:4200",
			code:        "invalid-code",
			state:       "valid-state",
			stateCookie: "valid-state",
			setupMock: func(m *MockAuthService) {
				// No mock needed as exchange will fail, but we want to test the createSessionWithCookie error path
				// This will trigger the error path in OAuth2Callback when processOAuth2CallbackLogic fails
			},
			expectedCode: http.StatusInternalServerError,
			expectedBody: `{"code":500,"data":null,"message":"Internal Server Error","status":"error"}`,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create handler with mock service
			mockService := &MockAuthService{}
			if tc.setupMock != nil {
				tc.setupMock(mockService)
			}
			handler := NewHandler(mockService)

			// Create request with query parameters
			req := httptest.NewRequest("GET", "/callback?code="+tc.code+"&state="+tc.state, nil)
			req.Host = tc.host

			// Add state cookie if provided
			if tc.stateCookie != "" {
				req.AddCookie(&http.Cookie{
					Name:  "oauth_state",
					Value: tc.stateCookie,
				})
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call the handler
			handler.OAuth2Callback(rr, req)

			// Assert status code
			assert.Equal(t, tc.expectedCode, rr.Code)

			// Assert response body
			body := strings.TrimSpace(rr.Body.String())
			assert.Equal(t, tc.expectedBody, body)
		})
	}
}

// TestHandleLogin_AdditionalHosts tests handleLogin with additional host patterns
func TestHandleLogin_AdditionalHosts(t *testing.T) {
	// Setup test environment variables for OIDC
	setupOIDCEnv(t)

	// Setup test OAuth2 configuration
	setupTestOAuth2Config(t)

	tests := []struct {
		name     string
		host     string
		wantCode int
	}{
		{
			name:     "onramp host environment",
			host:     "onramp:4200",
			wantCode: http.StatusFound,
		},
		{
			name:     "localhost with different port",
			host:     "localhost:8080",
			wantCode: http.StatusFound,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/login", nil)
			req.Host = tc.host
			rr := httptest.NewRecorder()

			handler := NewHandler(&MockAuthService{})
			handler.OAuth2Login(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code)

			// Check that state cookie was set
			cookies := rr.Result().Cookies()
			var stateCookie *http.Cookie
			for _, cookie := range cookies {
				if cookie.Name == "oauth_state" {
					stateCookie = cookie
					break
				}
			}
			assert.NotNil(t, stateCookie, "oauth_state cookie should be set")
			assert.NotEmpty(t, stateCookie.Value, "oauth_state cookie should have a value")

			// Check redirect location contains expected parameters
			location := rr.Header().Get("Location")
			assert.Contains(t, location, "state=", "redirect should contain state parameter")
		})
	}
}

// TestHandleLogout_AdditionalHosts tests handleLogout with additional host patterns
func TestHandleLogout_AdditionalHosts(t *testing.T) {
	// Setup test environment variables for OIDC
	setupOIDCEnv(t)

	// Create handler with its own session store
	handler := NewHandler(&MockAuthService{})

	// Pre-populate session store using proper interface
	validToken := &oauth2.Token{AccessToken: "test-access-token"}
	handler.GetSessionStore().SetSession("test-session-id", &session.SessionData{
		UserID:          "test-user",
		OAuthToken:      validToken,
		UserPermissions: nil,
		Data:            make(map[string]any),
	})

	// Setup test OAuth2 configuration
	setupTestOAuth2Config(t)

	tests := []struct {
		name          string
		host          string
		sessionCookie *http.Cookie
		wantCode      int
	}{
		{
			name:          "logout with localhost different port",
			host:          "localhost:8080",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "test-session-id"},
			wantCode:      http.StatusFound,
		},
		{
			name:          "logout with different host pattern",
			host:          "test.example.com",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "test-session-id"},
			wantCode:      http.StatusFound,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/logout", nil)
			req.Host = tc.host
			if tc.sessionCookie != nil {
				req.AddCookie(tc.sessionCookie)
			}
			rr := httptest.NewRecorder()

			handler.OAuth2Logout(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code)

			// Check that session cookie is cleared
			cookies := rr.Result().Cookies()
			var sessionCookie *http.Cookie
			for _, cookie := range cookies {
				if cookie.Name == "session_id" {
					sessionCookie = cookie
					break
				}
			}
			if sessionCookie != nil {
				assert.Empty(t, sessionCookie.Value, "session cookie should be cleared")
				assert.True(t, sessionCookie.MaxAge < 0, "session cookie should be expired")
			}
		})
	}
}
