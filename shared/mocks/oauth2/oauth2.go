package oauth2

import (
	"context"
	"fmt"

	"golang.org/x/oauth2"
	"github.com/coreos/go-oidc/v3/oidc"
)

var (
	ErrFakeOAuth2ConfigExchangeCallLimit = fmt.Errorf("Exchange call limit reached")
	ErrFakeIDTokenVerifierVerifyCallLimit = fmt.Errorf("Verify call limit reached")
	ErrFakeIDTokenClaimsCallLimit = fmt.Errorf("Claims call limit reached")
)

// FakeOAuth2Config mocks oauth2.Config for testing
type FakeOAuth2Config struct {
	EnableFailAfter       bool
	ExchangeFunc          func(ctx context.Context, code string) (*oauth2.Token, error)
	ExchangeCallCount     int
	ExchangeCallFailAfter int
	
	// Mock data to return
	MockToken *oauth2.Token
	MockError error
}

func (f *FakeOAuth2Config) Exchange(ctx context.Context, code string) (*oauth2.Token, error) {
	f.ExchangeCallCount++
	if f.ExchangeFunc != nil {
		return f.ExchangeFunc(ctx, code)
	}
	if f.EnableFailAfter && f.ExchangeCallFailAfter >= 0 && f.ExchangeCallCount > f.ExchangeCallFailAfter {
		return nil, ErrFakeOAuth2ConfigExchangeCallLimit
	}
	if f.MockError != nil {
		return nil, f.MockError
	}
	if f.MockToken != nil {
		return f.MockToken, nil
	}
	// Default successful response
	return &oauth2.Token{
		AccessToken: "test-access-token",
		TokenType:   "Bearer",
	}, nil
}

// FakeIDTokenVerifier mocks oidc.IDTokenVerifier for testing
type FakeIDTokenVerifier struct {
	EnableFailAfter     bool
	VerifyFunc          func(ctx context.Context, rawIDToken string) (*oidc.IDToken, error)
	VerifyCallCount     int
	VerifyCallFailAfter int
	
	// Mock data to return
	MockIDToken *FakeIDToken
	MockError   error
}

func (f *FakeIDTokenVerifier) Verify(ctx context.Context, rawIDToken string) (*oidc.IDToken, error) {
	f.VerifyCallCount++
	if f.VerifyFunc != nil {
		return f.VerifyFunc(ctx, rawIDToken)
	}
	if f.EnableFailAfter && f.VerifyCallFailAfter >= 0 && f.VerifyCallCount > f.VerifyCallFailAfter {
		return nil, ErrFakeIDTokenVerifierVerifyCallLimit
	}
	if f.MockError != nil {
		return nil, f.MockError
	}
	// We can't easily return a real oidc.IDToken, so we'll need to handle this differently
	// For now, return nil and let the test handle the mock
	return nil, nil
}

// FakeIDToken mocks oidc.IDToken for testing
type FakeIDToken struct {
	EnableFailAfter     bool
	ClaimsFunc          func(v interface{}) error
	ClaimsCallCount     int
	ClaimsCallFailAfter int
	
	// Mock data to return
	MockClaims map[string]interface{}
	MockError  error
}

func (f *FakeIDToken) Claims(v interface{}) error {
	f.ClaimsCallCount++
	if f.ClaimsFunc != nil {
		return f.ClaimsFunc(v)
	}
	if f.EnableFailAfter && f.ClaimsCallFailAfter >= 0 && f.ClaimsCallCount > f.ClaimsCallFailAfter {
		return ErrFakeIDTokenClaimsCallLimit
	}
	if f.MockError != nil {
		return f.MockError
	}
	if f.MockClaims != nil {
		if claimsPtr, ok := v.(*map[string]interface{}); ok {
			*claimsPtr = f.MockClaims
			return nil
		}
	}
	// Default successful response
	if claimsPtr, ok := v.(*map[string]interface{}); ok {
		*claimsPtr = map[string]interface{}{
			"sub":   "test-subject",
			"iss":   "test-issuer",
			"name":  "Test User",
			"email": "<EMAIL>",
		}
		return nil
	}
	return fmt.Errorf("invalid claims type")
}

// CreateMockOAuth2Token creates a mock OAuth2 token with id_token for testing
func CreateMockOAuth2Token(accessToken string, idToken string) *oauth2.Token {
	token := &oauth2.Token{
		AccessToken: accessToken,
		TokenType:   "Bearer",
	}
	// Add id_token as extra field
	if idToken != "" {
		token = token.WithExtra(map[string]interface{}{
			"id_token": idToken,
		})
	}
	return token
}

// NewFakeOAuth2Config creates a new FakeOAuth2Config with default settings
func NewFakeOAuth2Config() *FakeOAuth2Config {
	return &FakeOAuth2Config{
		MockToken: CreateMockOAuth2Token("test-access-token", "mock-id-token"),
	}
}

// NewFakeIDTokenVerifier creates a new FakeIDTokenVerifier with default settings
func NewFakeIDTokenVerifier() *FakeIDTokenVerifier {
	return &FakeIDTokenVerifier{
		MockIDToken: &FakeIDToken{
			MockClaims: map[string]interface{}{
				"sub":   "test-subject",
				"iss":   "test-issuer",
				"name":  "Test User",
				"email": "<EMAIL>",
			},
		},
	}
}

// NewFakeIDToken creates a new FakeIDToken with default settings
func NewFakeIDToken() *FakeIDToken {
	return &FakeIDToken{
		MockClaims: map[string]interface{}{
			"sub":   "test-subject",
			"iss":   "test-issuer",
			"name":  "Test User",
			"email": "<EMAIL>",
		},
	}
}
