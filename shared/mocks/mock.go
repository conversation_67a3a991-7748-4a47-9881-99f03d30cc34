package mocks

import (
	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"

	connect "synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/bqbatcher"
	"synapse-its.com/shared/mocks/bqexecutor"
	"synapse-its.com/shared/mocks/dbexecutor"
	"synapse-its.com/shared/mocks/firestore"
	"synapse-its.com/shared/mocks/healthz"
	"synapse-its.com/shared/mocks/oauth2"
	"synapse-its.com/shared/mocks/pubsub"
	"synapse-its.com/shared/mocks/schemaexecutor"
)

// FakeBigQueryExecutor mocks /shared/connect/bigquery
type FakeBigQueryExecutor = bqexecutor.FakeBigQueryExecutor

// FakeDBExecutor mocks /shared/connect/postgres
type FakeDBExecutor = dbexecutor.FakeDBExecutor

// FakeHealthzServer mocks /shared/healthz
type FakeHealthzServer = healthz.FakeHealthzServer

// FakePubsubClient mocks /shared/connect/pubsub
type (
	FakePubsubClient = pubsub.FakePubsubClient
	FakePubsubTopic  = pubsub.FakePubsubTopic
)

// Creates a new FakePubsubClient
var NewFakePubsubClient = pubsub.NewFakePubsubClient

// FakeBatcher mocks /shared/bqbatch
type (
	FakeBatcher     = bqbatcher.FakeBatcher
	FakeBatchOption = bqbatcher.FakeBatchOption
)

var (
	FakeBatch              = bqbatcher.FakeBatch
	FakeBatcherWithOptions = bqbatcher.FakeBatcherWithOptions
	WithBatchShutdownError = bqbatcher.WithBatchShutdownError
)

type FakeSchemaMigrationExecutor = schemaexecutor.FakeSchemaMigrationExecutor

// Create a new FakeFirestoreClient
var NewFakeFirestoreClient = firestore.NewFakeFirestoreClient

type FakeFirestoreClient = firestore.FakeFirestoreClient

// FakeOAuth2 mocks OAuth2 components
type (
	FakeOAuth2Config    = oauth2.FakeOAuth2Config
	FakeIDTokenVerifier = oauth2.FakeIDTokenVerifier
	FakeIDToken         = oauth2.FakeIDToken
)

var (
	NewFakeOAuth2Config    = oauth2.NewFakeOAuth2Config
	NewFakeIDTokenVerifier = oauth2.NewFakeIDTokenVerifier
	NewFakeIDToken         = oauth2.NewFakeIDToken
	CreateMockOAuth2Token  = oauth2.CreateMockOAuth2Token
)

// FakeConns returns a valid *connect.Connections with a default FakeDBExecutor.
func FakeConns() *connect.Connections {
	// Create a fake Redis server using miniredis
	// This is more robust than mocking individual commands
	mr, _ := miniredis.Run()

	redisClient := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})

	return &connect.Connections{
		Redis:     redisClient,
		Pubsub:    NewFakePubsubClient(),
		Postgres:  &FakeDBExecutor{},
		Bigquery:  &FakeBigQueryExecutor{},
		Firestore: NewFakeFirestoreClient(),
	}
}
